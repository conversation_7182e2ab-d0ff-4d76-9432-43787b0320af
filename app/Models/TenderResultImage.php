<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Storage;
use Illuminate\Database\Eloquent\Concerns\HasUlids;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class TenderResultImage extends Model
{
    use HasFactory;
    use HasUlids;
    
    /**
     * The primary key for the model.
     *
     * @var string
     */
    protected $primaryKey = 'id';
    
    /**
     * The "type" of the primary key ID.
     *
     * @var string
     */
    protected $keyType = 'string';

    protected $fillable = [
        'tender_result_id',
        'title',
        'path',
        'size',
        'extension',
        'meta',
        'latitude',
        'longitude',
        'location_name',
        'gps_timestamp',
    ];

    protected $casts = [
        'meta' => 'array',
        'gps_timestamp' => 'datetime',
    ];

    public function tenderResult()
    {
        return $this->belongsTo(TenderResult::class);
    }

    public function getFilePathAttribute()
    {
        if (!$this->path) {
            return null;
        }
        
        // Check if the path uses the public disk
        if (strpos($this->path, 'tender-progress-images') === 0) {
            return Storage::disk('public')->url($this->path);
        } 
        
        // Fall back to the tender-documents disk for any existing records
        try {
            return Storage::disk('tender-documents')->temporaryUrl($this->path, now()->addMinutes(2));
        } catch (\Exception $e) {
            logger()->error('Error generating URL for path: ' . $this->path . ': ' . $e->getMessage());
            return null;
        }
    }

    public function getSizeFormattedAttribute()
    {
        return $this->size 
            ? \Illuminate\Support\Str::bytesToHuman($this->size)
            : $this->size;
    }
    
    public function getHasGpsDataAttribute()
    {
        return !is_null($this->latitude) && !is_null($this->longitude);
    }
    
    public function getLocationFormattedAttribute()
    {
        if ($this->location_name) {
            return $this->location_name;
        }
        
        if ($this->has_gps_data) {
            return "Lat: {$this->latitude}, Long: {$this->longitude}";
        }
        
        return 'No location data';
    }
}