<?php

namespace App\Models;

use App\Traits\WithRoles;
use Illuminate\Database\Eloquent\Concerns\HasUlids;
use <PERSON><PERSON>\Sanctum\HasApiTokens;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Facades\Storage;
use Illuminate\Notifications\Notifiable;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;

class User extends Authenticatable
{
    use HasFactory;
    use Notifiable;
    use WithRoles;
    use HasApiTokens;
    use HasUlids;

    const ROLE_SUPER_ADMIN = 'super-admin';
    const ROLE_ADMIN = 'admin';
    const ROLE_MAKER = 'maker';
    const ROLE_CHECKER = 'checker';
    const ROLE_COMPANY = 'company';
    const ROLE_USER = 'user';
    const ROLE_EVALUATOR= 'evaluator';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'name',
        'department_id',
        'email',
        'password',
        'photo',
        // 'gender',
        'phone',
        'dob',
        'about',
        'role',
        'permissions',
        'meta',
        'last_online_at',
        'blocked_at'
    ];

    /**
     * The attributes that should be hidden for arrays.
     *
     * @var array
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
        'last_online_at' => 'datetime',
        'permissions' => 'array',
        'meta' => 'array',
        'dob' => 'date'
    ];

    protected $appends = [
        'photo_url',
        'role_color',
        'user_permissions',
    ];

    public function getUserPermissionsAttribute()
    {
        return [
            'create' => Gate::allows('create', $this),
            'read' => Gate::allows('view', $this),
            'update' => Gate::allows('update', $this),
            'delete' => Gate::allows('delete', $this),
        ];
    }

    public function getPhotoUrlAttribute()
    {
        return $this->photo
            ? Storage::disk('profile')->url($this->photo)
            : 'https://avatars.dicebear.com/api/initials/'. urlencode(trim($this->name)) .'.svg?&width=64&height=64';
        // return 'https://avatars.dicebear.com/api/initials/'. urlencode(trim($this->name)) .'.svg?&width=64&height=64';
    }

    public function getRoleColorAttribute()
    {
        return [
            self::ROLE_SUPER_ADMIN => 'danger',
            self::ROLE_ADMIN => 'danger',
            self::ROLE_MAKER => 'success',
            self::ROLE_CHECKER => 'warning',
            self::ROLE_USER => 'info',
            self::ROLE_COMPANY => 'info',
            self::ROLE_EVALUATOR => 'info',
        ][$this->role ?? self::ROLE_USER];
    }

    public function department()
    {
        return $this->belongsTo(Department::class);
    }

    public function companyProfile()
    {
        return $this->hasOne(CompanyProfile::class);
    }

    public function tenders(): BelongsToMany
    {
        return $this->belongsToMany(Tender::class, 'evaluator_tender', 'evaluator_id', 'tender_id');
    }

    public function routeNotificationForFcm($notification)
    {
        // return $this->tokens();
        return $this->tokens()->pluck('name')->toArray();
    }

    public function routeNotificationForSms($notification)
    {
        return $this->phone;
    }
}
