<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Storage;
use App\Models\Department;
use App\Models\TenderResultImage;

class MigrateFilesToS3 extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'files:migrate-to-s3 
                            {--type=all : Type of files to migrate (all, departments, progress-images)}
                            {--dry-run : Show what would be migrated without actually doing it}
                            {--delete-local : Delete local files after successful migration}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Migrate files from local storage to S3';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $type = $this->option('type');
        $dryRun = $this->option('dry-run');
        $deleteLocal = $this->option('delete-local');

        $this->info('Starting file migration to S3...');
        
        if ($dryRun) {
            $this->warn('DRY RUN MODE - No files will be actually migrated');
        }

        if ($deleteLocal && !$dryRun) {
            $this->warn('Local files will be deleted after successful migration');
        }

        switch ($type) {
            case 'departments':
                $this->migrateDepartmentPhotos($dryRun, $deleteLocal);
                break;
            case 'progress-images':
                $this->migrateTenderProgressImages($dryRun, $deleteLocal);
                break;
            case 'all':
            default:
                $this->migrateDepartmentPhotos($dryRun, $deleteLocal);
                $this->migrateTenderProgressImages($dryRun, $deleteLocal);
                break;
        }

        $this->info('File migration completed!');
        return 0;
    }

    /**
     * Migrate department photos from public disk to S3
     */
    private function migrateDepartmentPhotos($dryRun = false, $deleteLocal = false)
    {
        $this->info('Migrating department photos...');
        
        $departments = Department::whereNotNull('photo')->get();
        $migrated = 0;
        $failed = 0;

        foreach ($departments as $department) {
            try {
                $oldPath = $department->photo;
                
                // Skip if already migrated (doesn't contain directory prefix)
                if (!str_contains($oldPath, '/')) {
                    $this->line("Skipping already migrated department photo: {$oldPath}");
                    continue;
                }
                
                // Check if file exists in public disk
                if (Storage::disk('public')->exists($oldPath)) {
                    $newPath = basename($oldPath);
                    
                    if ($dryRun) {
                        $this->line("Would migrate: {$oldPath} -> {$newPath}");
                        continue;
                    }
                    
                    // Get file content
                    $fileContent = Storage::disk('public')->get($oldPath);
                    
                    // Store in S3
                    Storage::disk('department-photos')->put($newPath, $fileContent);
                    
                    // Update database record
                    $department->update(['photo' => $newPath]);
                    
                    $this->line("✓ Migrated department photo: {$oldPath} -> {$newPath}");
                    $migrated++;
                    
                    // Delete from public disk if requested
                    if ($deleteLocal) {
                        Storage::disk('public')->delete($oldPath);
                        $this->line("  Deleted local file: {$oldPath}");
                    }
                } else {
                    $this->error("Department photo not found: {$oldPath}");
                    $failed++;
                }
            } catch (\Exception $e) {
                $this->error("Failed to migrate department photo {$department->id}: " . $e->getMessage());
                $failed++;
            }
        }

        $this->info("Department photos - Migrated: {$migrated}, Failed: {$failed}");
    }

    /**
     * Migrate tender progress images from public disk to S3
     */
    private function migrateTenderProgressImages($dryRun = false, $deleteLocal = false)
    {
        $this->info('Migrating tender progress images...');
        
        $images = TenderResultImage::where('path', 'like', 'tender-progress-images%')->get();
        $migrated = 0;
        $failed = 0;

        foreach ($images as $image) {
            try {
                $oldPath = $image->path;
                
                // Check if file exists in public disk
                if (Storage::disk('public')->exists($oldPath)) {
                    // Generate new path for S3 (remove the directory prefix)
                    $newPath = str_replace('tender-progress-images/', '', $oldPath);
                    
                    if ($dryRun) {
                        $this->line("Would migrate: {$oldPath} -> {$newPath}");
                        continue;
                    }
                    
                    // Get file content
                    $fileContent = Storage::disk('public')->get($oldPath);
                    
                    // Store in S3
                    Storage::disk('progress-images')->put($newPath, $fileContent);
                    
                    // Update database record
                    $image->update(['path' => $newPath]);
                    
                    $this->line("✓ Migrated progress image: {$oldPath} -> {$newPath}");
                    $migrated++;
                    
                    // Delete from public disk if requested
                    if ($deleteLocal) {
                        Storage::disk('public')->delete($oldPath);
                        $this->line("  Deleted local file: {$oldPath}");
                    }
                } else {
                    $this->error("Progress image not found: {$oldPath}");
                    $failed++;
                }
            } catch (\Exception $e) {
                $this->error("Failed to migrate progress image {$image->id}: " . $e->getMessage());
                $failed++;
            }
        }

        $this->info("Progress images - Migrated: {$migrated}, Failed: {$failed}");
    }
}
