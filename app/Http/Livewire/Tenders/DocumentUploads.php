<?php

namespace App\Http\Livewire\Tenders;

use App\Models\Tender;
use App\Models\Upload;
use Livewire\Component;
use Livewire\WithFileUploads;
use Illuminate\Validation\Rules\File;

class DocumentUploads extends Component
{
    use WithFileUploads;

    public $tenderId;
    public $title;
    public $document;

    protected $listeners = [
        'refreshTenderDocuments' => '$refresh'
    ];

    public function saveDocument()
    {
        $validated = $this->validate([
            'title' => ['required', 'string'],
            'document' => ['required', 'file', 'mimes:pdf', 'max:8192'],
            // 'document' => [
            //     'required', 
            //     File::types(['pdf'])->max(3 * 1024),
            // ],
        ], [
            'document.max' => "Maximum file size to upload is 8MB. If you are uploading a pdf, try to reduce its size to make it under 8MB"
        ]);

        $this->tender->documents()->create([
            'title' => $validated['title'],
            // 'path' => $this->document->store('tender-uploads', 'public'),
            // 'size' => number_format($this->document->getSize() / 1048576, 2),
            'path' => $this->document->store('/', 'tender-documents'),
            'size' => $this->document->getSize(),
            'extension' => $this->document->getClientOriginalExtension(),
        ]);

        $this->reset('title');
        $this->dispatchBrowserEvent('destroy-filepond');

        $this->emit('refreshTenderDocuments');
        $this->emit('refreshTenders');
    }

    public function deleteDocument($documentId)
    {
        $document = Upload::findOrFail($documentId);
        $document->delete();

        // TODO: delete from storage as well

        $this->emit('refreshTenderDocuments');
        $this->emit('refreshTenders');
    }

    public function getTenderProperty()
    {
        return Tender::findOrFail($this->tenderId);
    }

    public function render()
    {
        return view('livewire.tenders.document-uploads', [
            'tenderDocuments' => $this->tender->documents
        ]);
    }
}
