<?php

namespace App\Http\Livewire\Front;

use App\Models\Tender;
use App\Models\Bidding;
use Livewire\Component;
use App\Models\EmdPayment;
use Livewire\WithFileUploads;
use App\Models\DocumentPayment;
use Illuminate\Validation\Rule;
use Illuminate\Support\Facades\DB;
use App\Traits\InteractsWithBanner;
use Illuminate\Support\Facades\Log;
use App\Rules\CheckValidPhoneNumber;
use App\Notifications\SuccessfullBidSubmission;

class TenderApply extends Component
{
    use WithFileUploads;
    use InteractsWithBanner;

    public $tenderId;
    public $tenderNumber;
    public $tenderUin;
    public $tenderFee;

    public $bidprices = [];
    public $tenderdocuments = [];
    public $name;
    public $email;
    public $phone;

    protected $listeners = [
        'refreshTender' => '$refresh'
    ];

    public function mount(Tender $tender)
    {
        // Check if this is a selective tender and if the current company has access
        if ($tender->isSelectiveTender()) {
            // Check if the company has access to this tender
            if (!$tender->companyCanAccessTender(auth()->user())) {
                session()->flash('error', 'You do not have access to this selective tender');
                return redirect()->route('front.tenders');
            }
        }

        $tender->loadMissing('tenderitems');

        $this->tenderId = $tender->id;
        $this->tenderNumber = $tender->tender_number;
        $this->tenderUin = $tender->tender_uin;
        $this->tenderFee = $tender->tender_cost;

        // Get the authenticated user directly instead of using property accessor
        $user = auth()->user();

        $this->name = $user->name;
        $this->email = $user->email;
        $this->phone = $user->phone;

        $this->fill([
            'tenderdocuments' => collect($this->tender?->document_types)->map(fn ($item) => [
                'id' => $item['id'],
                'size' => $item['size'],
                'type' => $item['type'],
                'documentName' => $item['documentName'],
                'document' => null,
            ])->all(),
            'bidprices' => $this->tender?->tenderitems?->map(fn ($item) => [
                'id' => $item->id,
                'item_type' => $item->item_type,
                'quantity' => $item->quantity,
                'unit' => $item->unit,
                'estimated_price' => $item->estimated_price,
                'price' => 0
            ])->all(),
        ]);
    }

    public function save()
    {
        $validated = $this->validate([
            'name' => ['required', 'string'],
            'email' => ['required', 'string', 'email'],
            'phone' => ['required', 'max:10', 'min:10', new CheckValidPhoneNumber],
            'bidprices' => ['required', 'array'],
            'bidprices.*.id' => ['required', Rule::in($this->inItems())],
            'bidprices.*.quantity' => ['required', Rule::in($this->inQuantities())],
            'bidprices.*.price' => ['required'],
            'tenderdocuments' => ['required', 'array'],
            'tenderdocuments.*.documentName' => ['required', Rule::in($this->inDocumentTypes())],
            'tenderdocuments.*.document' => ['required', 'mimes:pdf'],
        ], [], [
            'bidprices.*.value' => 'bidprice',
            'tenderdocuments.*.document' => 'tender document'
        ]);

        try {
            return DB::transaction(function () use ($validated) {
                $bidding = Bidding::create([
                    'tender_id' => $this->tenderId,
                    'user_id' => $this->user->id,
                    'company_name' => $validated['name'],
                    'company_email' => $validated['email'],
                    'company_phone' => $validated['phone']
                ]);

                $emdPayment = EmdPayment::query()
                    ->where('tender_id', $this->tenderId)
                    ->where('user_id', $this->user->id)
                    ->first();

                if ($emdPayment) {
                    $emdPayment->update(['bidding_id' => $bidding->id]);
                }

                $bidding->pricings()->createMany(
                    collect($validated['bidprices'])->map(fn ($item) => [
                        'tenderitem_id' => $item['id'],
                        'bidding_price' => str_replace(',', '', $item['price']) * $item['quantity'],
                        'quantity' => $item['quantity'],
                        'rate' => str_replace(',', '', $item['price']),
                    ])->all()
                );

                $bidding->biddingDocuments()->createMany(
                    collect($validated['tenderdocuments'])->map(fn ($item) => [
                        'document_type' => $item['documentName'],
                        'document_path' => $item['document']->store('/', 'applied-documents'),
                    ])->all()
                );

                $this->banner('Tender applied successfully.');

                $this->user->notify(new SuccessfullBidSubmission($this->tenderUin));

                return redirect()->route('front.tenders.show', $this->tenderId);
            });
        } catch (\Exception $e) {
            // dd($e->getMessage());
            Log::error($e->getMessage());
            $this->notify('Something went wrong. Try again.', 'error');
        }
    }

    protected function inItems()
    {
        if ($this->tender->tenderitems->isNotEmpty()) {
            return $this->tender->tenderitems->pluck('id')->all();
        } else {
            return [];
        }
    }

    protected function inQuantities()
    {
        if ($this->tender->tenderitems->isNotEmpty()) {
            return $this->tender->tenderitems->pluck('quantity')->all();
        } else {
            return [];
        }
    }

    protected function inDocumentTypes()
    {
        // if (count($this->tender->document_types)) {
        if (! is_null($this->tender->document_types)) {
            return collect($this->tender->document_types)->pluck('documentName')->all();
        } else {
            return [];
        }
    }

    public function getTenderProperty()
    {
        return Tender::with('tenderitems')->findOrFail($this->tenderId);
    }

    public function getUserProperty()
    {
        return auth()->user();
    }

    public function getDocumentPaymentProperty()
    {
        return DocumentPayment::query()
            ->where('user_id', $this->user->id)
            ->where('tender_id', $this->tenderId)
            ->latest('created_at')
            ->first();
    }

    public function hasPaidTenderFee()
    {
        return $this->documentPayment
            && ! is_null($this->documentPayment->status)
            && (
                $this->documentPayment->status->statusLabel() === 'Paid'
            ) ? true : false;
    }

    public function render()
    {
        return view('livewire.front.tender-apply')
            ->layout('layouts.front', [
                'title' => 'Tender Apply'
            ]);
    }
}
