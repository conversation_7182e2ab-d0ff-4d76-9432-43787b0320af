<?php

namespace App\Http\Livewire\Departments;

use App\Models\Department;
use App\Http\Livewire\Modal;
use Livewire\WithFileUploads;

class Create extends Modal
{
    use WithFileUploads;

    public $name;
    public $photo;

    public function save()
    {
        $validated = $this->validate([
            'name' => ['required', 'string', 'unique:departments'],
            'photo' => ['nullable', 'image', 'max:2024']
        ]);

        Department::create([
            'name' => $validated['name'],
            'photo' => $validated['photo'] ? $validated['photo']->store('department', 'public') : null,
        ]);

        $this->closeModal();

        $this->reset('name');

        $this->dispatchBrowserEvent('destroy-filepond');
        $this->emit('refreshDepartments');

        $this->notify('Department added.');
    }

    public function render()
    {
        return view('livewire.departments.create');
    }
}
