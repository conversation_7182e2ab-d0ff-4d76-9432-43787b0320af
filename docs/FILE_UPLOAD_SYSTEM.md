# File Upload System Documentation

## Overview

The eTender Legacy application uses Amazon S3 for file storage with dedicated disk configurations for different file types. All file uploads are now consistently stored in S3 with proper temporary URL generation for security.

## S3 Disk Configurations

The following S3 disks are configured in `config/filesystems.php`:

| Disk Name | S3 Path | Purpose |
|-----------|---------|---------|
| `tender-documents` | `etender/tender-documents` | Tender PDF documents |
| `applied-documents` | `etender/applied-documents` | Bidding documents |
| `tender-emd` | `etender/emd` | EMD payment photos |
| `profile` | `etender/profile` | User profile photos |
| `minutes-documents` | `etender/minutes-documents` | Pre-bid meeting minutes |
| `aoc-documents` | `etender/aoc-documents` | AOC documents |
| `comparative-statements` | `etender/comparative-statements` | Comparative statements |
| `progress-images` | `etender/progress-images` | Tender progress images |
| `department-photos` | `etender/departments` | Department logos |
| `api-uploads` | `etender/api-uploads` | API temporary uploads |

## File Upload Components

### 1. Tender Document Uploads
- **Component**: `app/Http/Livewire/Tenders/DocumentUploads.php`
- **Model**: `app/Models/Upload.php`
- **Storage**: `tender-documents` disk
- **File Types**: PDF only
- **Max Size**: 8MB

### 2. Tender Progress Images
- **Component**: `app/Http/Livewire/Company/UploadTenderImages.php`
- **Model**: `app/Models/TenderResultImage.php`
- **Storage**: `progress-images` disk
- **File Types**: Images (jpg, jpeg, png)
- **Max Size**: 8MB
- **Features**: GPS metadata support

### 3. Department Photos
- **Component**: `app/Http/Livewire/Departments/Create.php`, `app/Http/Livewire/Departments/Edit.php`
- **Model**: `app/Models/Department.php`
- **Storage**: `department-photos` disk
- **File Types**: Images
- **Max Size**: 2MB

### 4. EMD Payment Photos
- **Component**: `app/Http/Livewire/Front/TenderEmdPayment.php`
- **Model**: `app/Models/EmdPayment.php`
- **Storage**: `tender-emd` disk
- **File Types**: Images
- **Max Size**: Varies

### 5. Pre-bid Meeting Minutes
- **Component**: `app/Http/Livewire/Tenders/PreBidMinutesUpload.php`
- **Model**: `app/Models/Tender.php` (field: `pre_bid_meeting_minutes`)
- **Storage**: `minutes-documents` disk
- **File Types**: PDF only
- **Max Size**: 8MB

### 6. Comparative Statements
- **Component**: `app/Http/Livewire/Tenders/ComparativeStatement.php`
- **Model**: `app/Models/Tender.php` (field: `comparative_statement`)
- **Storage**: `comparative-statements` disk
- **File Types**: PDF only
- **Max Size**: 8MB

### 7. AOC Documents
- **Component**: `app/Http/Livewire/Tenders/Result.php`
- **Model**: `app/Models/TenderResult.php` (field: `aoc_document`)
- **Storage**: `aoc-documents` disk
- **File Types**: PDF only
- **Max Size**: 8MB

## Security Features

### Temporary URLs
All S3 files use temporary URLs with expiration times:
- **Standard expiration**: 5 minutes
- **Short expiration**: 2 minutes (for some legacy components)

### File Validation
- File type validation using MIME types
- File size limits enforced
- Virus scanning (recommended for production)

## Migration from Local Storage

### Automatic Migration
Run the migration command to move existing files from local storage to S3:

```bash
# Dry run to see what would be migrated
php artisan files:migrate-to-s3 --dry-run

# Migrate all files
php artisan files:migrate-to-s3

# Migrate specific file types
php artisan files:migrate-to-s3 --type=departments
php artisan files:migrate-to-s3 --type=progress-images

# Migrate and delete local files
php artisan files:migrate-to-s3 --delete-local
```

### Database Migration
Run the database migration to update file paths:

```bash
php artisan migrate
```

## Livewire Configuration

### Temporary File Uploads
Livewire is configured to use local disk for temporary uploads to avoid CORS issues:

```php
// config/livewire.php
'temporary_file_upload' => [
    'disk' => env('LIVEWIRE_TEMPORARY_UPLOAD_DISK', 'local'),
    // ...
]
```

This is intentional - files are temporarily stored locally during upload, then moved to S3 when saved.

## Best Practices

### 1. File Storage
- Always use appropriate S3 disk for file type
- Store files in root directory of the disk (`/`)
- Use descriptive file names when possible

### 2. URL Generation
- Always use temporary URLs for S3 files
- Set appropriate expiration times
- Handle exceptions gracefully with fallbacks

### 3. Error Handling
- Log all S3 operation failures
- Provide user-friendly error messages
- Implement fallback mechanisms for legacy files

### 4. File Validation
- Validate file types and sizes before upload
- Use consistent validation rules across components
- Implement additional security checks as needed

## Troubleshooting

### Common Issues

1. **CORS Errors**: Ensure S3 bucket has proper CORS configuration
2. **Permission Errors**: Verify AWS credentials and bucket permissions
3. **File Not Found**: Check if file exists in correct S3 disk
4. **URL Generation Fails**: Verify S3 configuration and credentials

### Debugging

Enable detailed logging for file operations:

```php
logger()->info('File operation details', [
    'disk' => $diskName,
    'path' => $filePath,
    'operation' => 'upload/download/delete'
]);
```

## Environment Variables

Required environment variables for S3 configuration:

```env
AWS_ACCESS_KEY_ID=your_access_key
AWS_SECRET_ACCESS_KEY=your_secret_key
AWS_DEFAULT_REGION=your_region
AWS_BUCKET=your_bucket_name
AWS_URL=your_s3_url
AWS_ENDPOINT=your_endpoint (optional)
AWS_USE_PATH_STYLE_ENDPOINT=false
```

## Performance Considerations

- Use appropriate file size limits
- Implement file cleanup for failed uploads
- Consider CDN for frequently accessed files
- Monitor S3 storage costs and usage
