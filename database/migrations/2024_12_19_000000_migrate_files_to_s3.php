<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\DB;
use App\Models\Department;
use App\Models\TenderResultImage;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // This migration moves existing files from local storage to S3
        // Run this carefully in production
        
        $this->migrateDepartmentPhotos();
        $this->migrateTenderProgressImages();
        
        echo "File migration to S3 completed.\n";
    }

    /**
     * Migrate department photos from public disk to S3
     */
    private function migrateDepartmentPhotos()
    {
        echo "Migrating department photos...\n";
        
        $departments = Department::whereNotNull('photo')->get();
        
        foreach ($departments as $department) {
            try {
                $oldPath = $department->photo;
                
                // Check if file exists in public disk
                if (Storage::disk('public')->exists($oldPath)) {
                    // Get file content
                    $fileContent = Storage::disk('public')->get($oldPath);
                    
                    // Generate new path for S3
                    $newPath = basename($oldPath);
                    
                    // Store in S3
                    Storage::disk('department-photos')->put($newPath, $fileContent);
                    
                    // Update database record
                    $department->update(['photo' => $newPath]);
                    
                    echo "Migrated department photo: {$oldPath} -> {$newPath}\n";
                    
                    // Optionally delete from public disk
                    // Storage::disk('public')->delete($oldPath);
                } else {
                    echo "Department photo not found in public disk: {$oldPath}\n";
                }
            } catch (\Exception $e) {
                echo "Failed to migrate department photo {$department->id}: " . $e->getMessage() . "\n";
            }
        }
    }

    /**
     * Migrate tender progress images from public disk to S3
     */
    private function migrateTenderProgressImages()
    {
        echo "Migrating tender progress images...\n";
        
        $images = TenderResultImage::where('path', 'like', 'tender-progress-images%')->get();
        
        foreach ($images as $image) {
            try {
                $oldPath = $image->path;
                
                // Check if file exists in public disk
                if (Storage::disk('public')->exists($oldPath)) {
                    // Get file content
                    $fileContent = Storage::disk('public')->get($oldPath);
                    
                    // Generate new path for S3 (remove the directory prefix)
                    $newPath = str_replace('tender-progress-images/', '', $oldPath);
                    
                    // Store in S3
                    Storage::disk('progress-images')->put($newPath, $fileContent);
                    
                    // Update database record
                    $image->update(['path' => $newPath]);
                    
                    echo "Migrated progress image: {$oldPath} -> {$newPath}\n";
                    
                    // Optionally delete from public disk
                    // Storage::disk('public')->delete($oldPath);
                } else {
                    echo "Progress image not found in public disk: {$oldPath}\n";
                }
            } catch (\Exception $e) {
                echo "Failed to migrate progress image {$image->id}: " . $e->getMessage() . "\n";
            }
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        // This would reverse the migration by moving files back to local storage
        // Implementation depends on specific requirements
        echo "Reverse migration not implemented. Manual intervention required.\n";
    }
};
